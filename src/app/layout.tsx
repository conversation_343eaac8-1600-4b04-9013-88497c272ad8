import type { Metada<PERSON> } from "next"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import Header from "@/components/header"
import { Toaster } from "react-hot-toast"

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <Header />
          {children}
          <Toaster
            position="bottom-right"
            toastOptions={{
              className: "vercel-toast",
              duration: 4000,
              style: {
                background: "var(--background)",
                color: "var(--foreground)",
                border: "1px solid var(--border)",
                borderRadius: "0.5rem",
                fontSize: "0.875rem",
                padding: "0.75rem 1rem",
              },
              success: {
                className: "vercel-toast vercel-toast-success",
              },
              error: {
                className: "vercel-toast vercel-toast-error",
              },
            }}
          />
        </ThemeProvider>
      </body>
    </html>
  )
}
